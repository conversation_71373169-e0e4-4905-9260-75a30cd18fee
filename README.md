# Gemma 3N Educational Chat API

A FastAPI-based educational assistant using the Google Gemma 3N E4B model with support for multimodal inputs (text, images, and audio) designed specifically for learning and academic assistance.

## Features

### 🎓 Educational Focus
- **Strict Educational Boundaries**: Only responds to educational and academic queries
- **Age-Appropriate Responses**: Tailored explanations for different educational levels
- **Subject-Specific Assistance**: Specialized help for various academic subjects
- **Learning-Oriented**: Encourages critical thinking rather than just providing answers

### 🔄 Multimodal Support
- **Text Input**: Direct text questions and conversations
- **Image Processing**: OCR text extraction and educational image analysis
- **Audio Processing**: Speech-to-text for voice questions
- **Combined Inputs**: Handle multiple input types in a single request

### 🚀 Technical Features
- **FastAPI Framework**: High-performance async API
- **Gemma 3N E4B Model**: Google's latest educational-focused language model
- **Session Management**: Conversation history and context tracking
- **Content Filtering**: Automatic detection and blocking of non-educational content
- **Scalable Architecture**: Modular design for easy extension and maintenance

## Quick Start

### Prerequisites
- Python 3.12+
- CUDA-compatible GPU (recommended) or CPU
- At least 8GB RAM (16GB+ recommended for GPU usage)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gemma-3n
   ```

2. **Install dependencies using uv**
   ```bash
   uv sync
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the application**
   ```bash
   python main.py
   ```

The API will be available at `http://localhost:8000` with interactive documentation at `http://localhost:8000/docs`.

## API Usage

### Basic Chat Request

```python
import requests
import base64

# Text-only request
response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "text",
            "content": "Can you help me understand photosynthesis?"
        }
    ],
    "educational_level": "high_school",
    "subject": "biology"
})

print(response.json())
```

### Multimodal Request (Text + Image)

```python
# Read and encode image
with open("math_problem.jpg", "rb") as f:
    image_data = base64.b64encode(f.read()).decode()

response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "text",
            "content": "Please solve this math problem:"
        },
        {
            "type": "image",
            "content": image_data,
            "mime_type": "image/jpeg",
            "filename": "math_problem.jpg"
        }
    ],
    "educational_level": "high_school",
    "subject": "mathematics",
    "include_reasoning": True
})
```

### Audio Input

```python
# Read and encode audio file
with open("question.wav", "rb") as f:
    audio_data = base64.b64encode(f.read()).decode()

response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "audio",
            "content": audio_data,
            "mime_type": "audio/wav",
            "filename": "question.wav"
        }
    ],
    "educational_level": "middle_school",
    "subject": "general"
})
```

## Educational Levels

- `elementary`: K-5 (ages 5-11)
- `middle_school`: 6-8 (ages 11-14)
- `high_school`: 9-12 (ages 14-18)
- `college`: Undergraduate level
- `graduate`: Graduate level
- `adult_learning`: Adult education

## Supported Subjects

- Mathematics
- Science (General)
- Physics
- Chemistry
- Biology
- History
- Geography
- English/Language Arts
- Computer Science
- Foreign Languages
- Art
- Music
- General (Cross-curricular)

## Configuration

Key configuration options in `.env`:

```env
# Model Configuration
MODEL_NAME=google/gemma-3n-E4B-it
DEVICE=auto  # auto, cpu, cuda
MAX_MODEL_MEMORY=8GB

# Input Limits
MAX_FILE_SIZE_MB=10
MAX_INPUT_LENGTH=4096

# Educational Settings
EDUCATIONAL_SYSTEM_PROMPT="Your custom educational prompt..."
```

## API Endpoints

### Chat
- `POST /chat/` - Main chat endpoint for educational assistance
- `POST /chat/stream` - Streaming chat (planned)

### Sessions
- `GET /sessions/{session_id}` - Get conversation history
- `DELETE /sessions/{session_id}` - Delete session
- `GET /sessions/` - List recent sessions

### Health
- `GET /health/` - Health check
- `GET /health/ready` - Readiness check

## Development

### Project Structure

```
gemma-3n/
├── src/gemma_3n/           # Main application package
│   ├── api.py              # FastAPI application
│   ├── core/               # Core models and configuration
│   ├── routes/             # API route handlers
│   └── services/           # Business logic services
├── main.py                 # Application entry point
├── pyproject.toml          # Project configuration
└── README.md              # This file
```

### Running Tests

```bash
# Install development dependencies
uv sync --group dev

# Run tests
pytest

# Run with coverage
pytest --cov=src/gemma_3n
```

### Code Quality

```bash
# Format code
black src/
isort src/

# Lint code
flake8 src/
mypy src/
```

## Deployment

### Production Considerations

1. **GPU Support**: Ensure CUDA drivers are installed for GPU acceleration
2. **Memory**: Allocate sufficient RAM/VRAM for the model
3. **Security**: Update `SECRET_KEY` and configure proper CORS origins
4. **Monitoring**: Enable metrics and logging for production monitoring
5. **Rate Limiting**: Configure appropriate rate limits for your use case

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

[Add your license information here]

## Support

For questions, issues, or contributions, please open an issue on GitHub.