# Gemma 3N Educational Chat API Configuration
# Copy this file to .env and update the values as needed

# API Configuration
APP_NAME="Gemma 3N Educational Chat API"
APP_VERSION="0.1.0"
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-secret-key-change-in-production
ALLOWED_ORIGINS=["*"]

# Model Configuration
MODEL_NAME=google/gemma-3-4b-it
MODEL_CACHE_DIR=./models
DEVICE=auto
MAX_MODEL_MEMORY=8GB

# Generation Parameters
DEFAULT_MAX_TOKENS=1024
MAX_MAX_TOKENS=2048
DEFAULT_TEMPERATURE=0.7
DEFAULT_TOP_P=0.9
DEFAULT_TOP_K=50
DEFAULT_REPETITION_PENALTY=1.1

# Input Processing
MAX_INPUT_LENGTH=4096
MAX_FILE_SIZE_MB=10
SUPPORTED_IMAGE_TYPES=["image/jpeg","image/png","image/gif","image/webp"]
SUPPORTED_AUDIO_TYPES=["audio/wav","audio/mp3","audio/m4a","audio/ogg"]

# OCR Configuration
OCR_ENGINE=easyocr
OCR_LANGUAGES=["en"]

# Audio Processing
AUDIO_SAMPLE_RATE=16000
AUDIO_CHUNK_LENGTH=30

# Database Configuration
DATABASE_URL=sqlite:///./conversations.db
REDIS_URL=redis://localhost:6379

# Session Management
SESSION_TIMEOUT_MINUTES=60
MAX_CONVERSATION_LENGTH=50

# Educational Assistant Configuration
EDUCATIONAL_SYSTEM_PROMPT="You are an educational assistant designed to help students learn and understand academic concepts. Your role is to: 1. Provide clear, age-appropriate explanations 2. Encourage critical thinking and learning 3. Guide students to discover answers rather than just providing them 4. Only respond to educational and academic queries 5. Refuse to help with non-educational content 6. Maintain a supportive and encouraging tone. Always focus on educational value and learning outcomes."

# Logging
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# SSL Configuration (optional)
# SSL_KEYFILE=/path/to/private.key
# SSL_CERTFILE=/path/to/certificate.crt
