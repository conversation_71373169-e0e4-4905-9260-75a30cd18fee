#!/usr/bin/env python3
"""
Main entry point for the Gemma 3N Educational Chat API.

This script starts the FastAPI server with the educational assistant
application configured for production or development use.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from gemma_3n import app
from gemma_3n.core.config import get_settings


def main():
    """Main entry point for the application."""
    settings = get_settings()

    # Configure uvicorn
    uvicorn_config = {
        "app": "main:app",
        "host": settings.host,
        "port": settings.port,
        "reload": settings.debug,
        "log_level": settings.log_level.lower(),
        "access_log": True,
    }

    # Add SSL configuration if certificates are available
    ssl_keyfile = os.getenv("SSL_KEYFILE")
    ssl_certfile = os.getenv("SSL_CERTFILE")

    if ssl_keyfile and ssl_certfile:
        uvicorn_config.update({
            "ssl_keyfile": ssl_keyfile,
            "ssl_certfile": ssl_certfile,
        })
        print(f"Starting HTTPS server on https://{settings.host}:{settings.port}")
    else:
        print(f"Starting HTTP server on http://{settings.host}:{settings.port}")

    print(f"Debug mode: {settings.debug}")
    print(f"Model: {settings.model_name}")
    print(f"API Documentation: http://{settings.host}:{settings.port}/docs")

    # Start the server
    uvicorn.run(**uvicorn_config)


if __name__ == "__main__":
    main()
