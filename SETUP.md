# Gemma 3N Educational Chat API - Setup Guide

This guide will help you set up and run the Gemma 3N Educational Chat API on your system.

## Prerequisites

### System Requirements
- **Python**: 3.12 or higher
- **RAM**: 8GB minimum, 16GB+ recommended
- **Storage**: 10GB+ free space for model cache
- **GPU**: NVIDIA GPU with 6GB+ VRAM (optional but recommended)

### Required Software
- [Python 3.12+](https://www.python.org/downloads/)
- [uv](https://docs.astral.sh/uv/) (Python package manager)
- Git (for cloning the repository)

## Installation Steps

### 1. Clone the Repository
```bash
git clone <repository-url>
cd gemma-3n
```

### 2. Install Dependencies
```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install project dependencies
uv sync
```

### 3. Configure Environment
```bash
# Copy the example environment file
cp .env.example .env

# Edit the configuration (see Configuration section below)
nano .env  # or use your preferred editor
```

### 4. Validate Installation
```bash
# Run the installation test
python test_installation.py

# Run the full setup validation
python scripts/validate_setup.py
```

## Configuration

### Basic Configuration

Edit the `.env` file to configure the application:

```env
# API Configuration
DEBUG=true                    # Set to false for production
HOST=0.0.0.0                 # Server host
PORT=8000                    # Server port

# Model Configuration
MODEL_NAME=google/gemma-3-4b-it    # Model to use
DEVICE=auto                  # auto, cpu, or cuda
MAX_MODEL_MEMORY=8GB         # Maximum GPU memory to use
```

### Model Selection

#### Option 1: Gemma 3N E4B (Recommended)
For the best educational experience, use the Gemma 3N E4B model:

1. **Get Hugging Face Access**:
   - Create account at [huggingface.co](https://huggingface.co)
   - Request access to [google/gemma-3n-E4B-it](https://huggingface.co/google/gemma-3n-E4B-it)
   - Wait for approval (usually quick)

2. **Authenticate**:
   ```bash
   # Install Hugging Face CLI
   pip install huggingface_hub

   # Login with your token
   huggingface-cli login
   ```

3. **Update Configuration**:
   ```env
   MODEL_NAME=google/gemma-3n-E4B-it
   ```

#### Option 2: Alternative Models (For Testing)
If you want to test immediately without waiting for Gemma access:

```env
# Smaller, publicly available models
MODEL_NAME=microsoft/DialoGPT-medium    # Good for testing
# or
MODEL_NAME=distilgpt2                   # Very small, fast
```

### Advanced Configuration

#### GPU Configuration
```env
DEVICE=cuda                  # Force GPU usage
MAX_MODEL_MEMORY=6GB         # Limit GPU memory usage
```

#### Educational Settings
```env
# Customize the educational assistant behavior
EDUCATIONAL_SYSTEM_PROMPT="Your custom educational prompt..."

# Input limits
MAX_INPUT_LENGTH=4096        # Maximum input characters
MAX_FILE_SIZE_MB=10          # Maximum file upload size
```

#### Multimodal Features
```env
# OCR Configuration
OCR_ENGINE=easyocr           # or tesseract
OCR_LANGUAGES=["en"]         # Supported languages

# Audio Processing
AUDIO_SAMPLE_RATE=16000      # Audio processing sample rate
AUDIO_CHUNK_LENGTH=30        # Maximum audio length in seconds
```

## Running the Application

### Development Mode
```bash
# Start the development server
python main.py
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health/

### Production Mode
```bash
# Update configuration for production
# Set DEBUG=false in .env
# Set a secure SECRET_KEY
# Configure proper CORS origins

# Start with production settings
python main.py
```

### Docker Deployment
```bash
# Build the Docker image
docker build -t gemma-3n .

# Run with Docker Compose
docker-compose up -d
```

## Testing the API

### Using the Web Interface
1. Open http://localhost:8000/docs
2. Try the `/chat/` endpoint
3. Send a test educational query

### Using curl
```bash
# Test basic chat
curl -X POST "http://localhost:8000/chat/" \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": [
      {
        "type": "text",
        "content": "Can you help me understand photosynthesis?"
      }
    ],
    "educational_level": "high_school",
    "subject": "biology"
  }'
```

### Using Python
```python
import requests

response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "text",
            "content": "Explain the Pythagorean theorem"
        }
    ],
    "educational_level": "high_school",
    "subject": "mathematics"
})

print(response.json())
```

## Troubleshooting

### Common Issues

#### 1. Model Access Denied
```
Error: 401 Client Error. Cannot access gated repo
```
**Solution**: Follow the Hugging Face authentication steps above.

#### 2. Out of Memory
```
Error: CUDA out of memory
```
**Solutions**:
- Reduce `MAX_MODEL_MEMORY` in .env
- Use a smaller model
- Use CPU mode: `DEVICE=cpu`

#### 3. Import Errors
```
Error: ModuleNotFoundError
```
**Solution**: Reinstall dependencies:
```bash
uv sync --reinstall
```

#### 4. Port Already in Use
```
Error: Address already in use
```
**Solution**: Change the port in .env:
```env
PORT=8001
```

### Performance Optimization

#### For GPU Users
```env
DEVICE=cuda
MAX_MODEL_MEMORY=6GB         # Adjust based on your GPU
```

#### For CPU Users
```env
DEVICE=cpu
DEFAULT_MAX_TOKENS=512       # Reduce for faster responses
```

#### For Limited RAM
```env
MODEL_NAME=distilgpt2        # Use smaller model
MAX_INPUT_LENGTH=2048        # Reduce input length
```

## Development

### Running Tests
```bash
# Install development dependencies
uv sync --group dev

# Run tests
pytest

# Run with coverage
pytest --cov=src/gemma_3n
```

### Code Quality
```bash
# Format code
black src/
isort src/

# Lint code
flake8 src/
mypy src/
```

## Support

### Getting Help
1. Check this setup guide
2. Review the troubleshooting section
3. Check the API documentation at `/docs`
4. Open an issue on GitHub

### Reporting Issues
When reporting issues, please include:
- Your system specifications
- Python version
- Error messages
- Configuration (without sensitive data)

## Next Steps

Once you have the API running:
1. Explore the interactive documentation at `/docs`
2. Try different educational queries
3. Test multimodal inputs (images, audio)
4. Integrate with your educational applications
5. Customize the educational prompts for your use case

Happy learning! 🎓
