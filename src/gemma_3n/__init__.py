"""
Gemma 3N - Educational Chat API with Multimodal Support

A FastAPI-based educational assistant using Gemma 3 model with support for
text, image, and audio inputs for learning and academic assistance.
"""

__version__ = "0.1.0"
__author__ = "Gemma 3N Team"
__description__ = "Educational Chat API with Gemma 3 - Multimodal AI Assistant for Learning"

from .api import app
from .core.models import (
    ChatRequest,
    ChatResponse,
    ConversationHistory,
    EducationalLevel,
    Subject,
    InputType,
    MessageRole,
    MessageType
)
from .core.config import get_settings
from .services import ModelManager, InputProcessor, ContentFilter, ChatService

__all__ = [
    "app",
    "ChatRequest",
    "ChatResponse",
    "ConversationHistory",
    "EducationalLevel",
    "Subject",
    "InputType",
    "MessageRole",
    "MessageType",
    "get_settings",
    "ModelManager",
    "InputProcessor",
    "ContentFilter",
    "ChatService",
]
