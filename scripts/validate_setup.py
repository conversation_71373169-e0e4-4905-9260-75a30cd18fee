#!/usr/bin/env python3
"""
Setup validation script for Gemma 3N Educational Chat API.

This script validates the installation, configuration, and system requirements
before starting the application.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))


def check_python_version():
    """Check Python version compatibility."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 12):
        print(f"❌ Python 3.12+ required, found {sys.version_info.major}.{sys.version_info.minor}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_dependencies():
    """Check if all required dependencies are available."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        ("fastapi", "FastAPI framework"),
        ("uvicorn", "ASGI server"),
        ("transformers", "Hugging Face Transformers"),
        ("torch", "PyTorch"),
        ("PIL", "Pillow (PIL)"),
        ("numpy", "NumPy"),
        ("pydantic", "Pydantic"),
        ("loguru", "Loguru logging"),
    ]
    
    optional_packages = [
        ("easyocr", "EasyOCR for image text extraction"),
        ("pytesseract", "Tesseract OCR"),
        ("speech_recognition", "Speech recognition"),
        ("pydub", "Audio processing"),
        ("cv2", "OpenCV"),
        ("redis", "Redis client"),
    ]
    
    missing_required = []
    missing_optional = []
    
    # Check required packages
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description}")
            missing_required.append(package)
    
    # Check optional packages
    for package, description in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package} - {description} (optional)")
        except ImportError:
            print(f"⚠️  {package} - {description} (optional, missing)")
            missing_optional.append(package)
    
    if missing_required:
        print(f"\n❌ Missing required packages: {', '.join(missing_required)}")
        print("Run: uv sync")
        return False
    
    if missing_optional:
        print(f"\n⚠️  Missing optional packages: {', '.join(missing_optional)}")
        print("Some features may not work properly.")
    
    return True


def check_system_resources():
    """Check system resources."""
    print("\n💻 Checking system resources...")
    
    try:
        import psutil
        
        # Check RAM
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        total_gb = memory.total / (1024**3)
        
        print(f"📊 RAM: {available_gb:.1f}GB available / {total_gb:.1f}GB total")
        
        if available_gb < 4:
            print("⚠️  Low available RAM. 8GB+ recommended for optimal performance.")
        elif available_gb >= 8:
            print("✅ Sufficient RAM available")
        else:
            print("⚠️  Moderate RAM available. Consider closing other applications.")
        
        # Check disk space
        disk = psutil.disk_usage('/')
        free_gb = disk.free / (1024**3)
        
        print(f"💾 Disk: {free_gb:.1f}GB free")
        
        if free_gb < 10:
            print("⚠️  Low disk space. At least 10GB recommended for model cache.")
        else:
            print("✅ Sufficient disk space")
        
    except ImportError:
        print("⚠️  psutil not available, skipping resource check")
    
    # Check GPU
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"🎮 GPU: {gpu_count} CUDA device(s) available")
            
            for i in range(gpu_count):
                props = torch.cuda.get_device_properties(i)
                memory_gb = props.total_memory / (1024**3)
                print(f"   GPU {i}: {props.name} ({memory_gb:.1f}GB)")
                
                if memory_gb < 4:
                    print(f"   ⚠️  GPU {i} has limited memory")
                else:
                    print(f"   ✅ GPU {i} has sufficient memory")
        else:
            print("🎮 GPU: No CUDA devices available (CPU mode)")
            
    except ImportError:
        print("⚠️  PyTorch not available, cannot check GPU")


def check_configuration():
    """Check configuration settings."""
    print("\n⚙️  Checking configuration...")
    
    try:
        from gemma_3n.core.config import get_settings
        
        settings = get_settings()
        print(f"✅ Configuration loaded successfully")
        
        # Validate settings
        warnings = settings.validate_settings()
        
        if warnings:
            print("⚠️  Configuration warnings:")
            for warning in warnings:
                print(f"   - {warning}")
        else:
            print("✅ Configuration validation passed")
        
        # Check model cache directory
        if os.path.exists(settings.model_cache_dir):
            print(f"✅ Model cache directory exists: {settings.model_cache_dir}")
        else:
            print(f"📁 Model cache directory will be created: {settings.model_cache_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def check_model_accessibility():
    """Check if the model can be accessed."""
    print("\n🤖 Checking model accessibility...")
    
    try:
        from gemma_3n.core.config import get_settings
        from transformers import AutoTokenizer
        
        settings = get_settings()
        
        print(f"📋 Model: {settings.model_name}")
        print("🔍 Checking model accessibility (this may take a moment)...")
        
        # Try to load tokenizer (lightweight check)
        tokenizer = AutoTokenizer.from_pretrained(
            settings.model_name,
            cache_dir=settings.model_cache_dir,
            trust_remote_code=True
        )
        
        print("✅ Model is accessible and tokenizer loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Model accessibility check failed: {e}")
        print("💡 This might be due to:")
        print("   - Network connectivity issues")
        print("   - Invalid model name")
        print("   - Missing authentication (for private models)")
        print("   - Insufficient disk space")
        return False


def main():
    """Run all validation checks."""
    print("🚀 Gemma 3N Educational Chat API - Setup Validation")
    print("=" * 60)
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("System Resources", check_system_resources),
        ("Configuration", check_configuration),
        ("Model Accessibility", check_model_accessibility),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} check failed with exception: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 60)
    print("📋 Validation Summary")
    print("=" * 60)
    
    passed = 0
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} checks passed")
    
    if passed == len(results):
        print("\n🎉 All validation checks passed!")
        print("✅ Your system is ready to run Gemma 3N Educational Chat API")
        print("\n🚀 Next steps:")
        print("   1. Run: python main.py")
        print("   2. Open: http://localhost:8000/docs")
        return True
    else:
        print("\n⚠️  Some validation checks failed.")
        print("🔧 Please address the issues above before starting the application.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
