# Google Gemma 3N E4B Integration Guide

This guide covers the integration and usage of Google's Gemma 3N E4B model in the Educational Chat API.

## About Gemma 3N E4B

The **Gemma 3N E4B** model is Google's latest educational-focused language model, specifically designed for:
- 📚 Educational content generation
- 🎓 Age-appropriate responses
- 🧠 Academic subject expertise
- 💡 Learning-oriented interactions

### Model Specifications
- **Model Name**: `google/gemma-3n-E4B-it`
- **Size**: ~15GB (4 model files)
- **Context Length**: 8192 tokens
- **Specialization**: Educational and instructional content
- **Languages**: Primarily English with educational focus

## Setup and Configuration

### 1. Hugging Face Access

The Gemma 3N E4B model requires access approval:

1. **Visit the Model Page**:
   - Go to: https://huggingface.co/google/gemma-3n-E4B-it
   - Click "Request Access"
   - Fill out the access request form
   - Wait for approval (typically within 24 hours)

2. **Authentication**:
   ```bash
   # Set your HF token in .env
   HF_TOKEN=your_huggingface_token_here
   ```

### 2. Model Configuration

Update your `.env` file:

```env
# Model Configuration
MODEL_NAME=google/gemma-3n-E4B-it
DEVICE=auto                    # Use GPU if available
MAX_MODEL_MEMORY=8GB          # Adjust based on your GPU
HF_TOKEN=your_token_here      # Your Hugging Face token
```

### 3. System Requirements

**Minimum Requirements**:
- RAM: 16GB+ (for CPU inference)
- GPU: 8GB+ VRAM (recommended)
- Storage: 20GB+ free space
- Network: Stable connection for initial download

**Recommended Requirements**:
- RAM: 32GB+
- GPU: NVIDIA RTX 4090 or similar (24GB+ VRAM)
- Storage: 50GB+ SSD space
- Network: High-speed connection

## Educational Features

### Enhanced Educational Capabilities

The Gemma 3N E4B model provides superior educational assistance:

1. **Subject Expertise**:
   - Mathematics (K-12 through college)
   - Science (Physics, Chemistry, Biology)
   - Language Arts and Literature
   - History and Social Studies
   - Computer Science and Programming

2. **Age-Appropriate Responses**:
   - Elementary (K-5): Simple, visual explanations
   - Middle School (6-8): Conceptual understanding
   - High School (9-12): Detailed analysis
   - College: Advanced academic content
   - Graduate: Research-level discussions

3. **Learning Methodologies**:
   - Socratic questioning
   - Step-by-step problem solving
   - Conceptual explanations
   - Real-world applications
   - Interactive learning

### Educational Prompt Engineering

The model responds well to educational prompts:

```python
# Example educational prompts
prompts = {
    "math": "Explain the concept of derivatives in calculus for a high school student",
    "science": "Describe photosynthesis using simple terms for a middle school student",
    "history": "Analyze the causes of World War I for a college-level history course",
    "programming": "Teach the concept of recursion with examples for a beginner"
}
```

## API Usage Examples

### Basic Educational Query

```python
import requests

response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "text",
            "content": "Can you explain quantum mechanics in simple terms?"
        }
    ],
    "educational_level": "high_school",
    "subject": "physics",
    "include_reasoning": True
})

print(response.json())
```

### Multimodal Educational Content

```python
import base64

# Image with mathematical equation
with open("math_problem.jpg", "rb") as f:
    image_data = base64.b64encode(f.read()).decode()

response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "text",
            "content": "Please solve this equation and explain each step:"
        },
        {
            "type": "image",
            "content": image_data,
            "mime_type": "image/jpeg",
            "filename": "math_problem.jpg"
        }
    ],
    "educational_level": "college",
    "subject": "mathematics",
    "include_reasoning": True
})
```

### Audio-Based Learning

```python
# Voice question about science
with open("science_question.wav", "rb") as f:
    audio_data = base64.b64encode(f.read()).decode()

response = requests.post("http://localhost:8000/chat/", json={
    "inputs": [
        {
            "type": "audio",
            "content": audio_data,
            "mime_type": "audio/wav",
            "filename": "science_question.wav"
        }
    ],
    "educational_level": "middle_school",
    "subject": "science"
})
```

## Performance Optimization

### GPU Configuration

For optimal performance with Gemma 3N E4B:

```env
# GPU Settings
DEVICE=cuda
MAX_MODEL_MEMORY=12GB         # Adjust based on your GPU
DEFAULT_MAX_TOKENS=1024       # Longer responses for detailed explanations
DEFAULT_TEMPERATURE=0.7       # Balanced creativity and accuracy
```

### CPU Configuration

For CPU-only inference:

```env
# CPU Settings
DEVICE=cpu
DEFAULT_MAX_TOKENS=512        # Shorter responses for faster generation
DEFAULT_TEMPERATURE=0.5       # More deterministic responses
```

## Educational Content Filtering

The API includes enhanced content filtering for educational appropriateness:

```python
# Educational content examples that pass filtering
educational_queries = [
    "How do I solve quadratic equations?",
    "Explain the water cycle",
    "What caused the American Civil War?",
    "How does DNA replication work?",
    "Teach me about Shakespeare's sonnets"
]

# Non-educational content that gets filtered
non_educational_queries = [
    "What's the weather today?",
    "Tell me a joke",
    "How to hack a computer?",
    "What's your favorite movie?"
]
```

## Troubleshooting

### Common Issues

1. **Model Access Denied**:
   ```
   Error: Cannot access gated repo
   ```
   **Solution**: Request access at the Hugging Face model page

2. **Out of Memory**:
   ```
   Error: CUDA out of memory
   ```
   **Solutions**:
   - Reduce `MAX_MODEL_MEMORY`
   - Use CPU mode: `DEVICE=cpu`
   - Reduce `DEFAULT_MAX_TOKENS`

3. **Slow Download**:
   ```
   Model downloading very slowly
   ```
   **Solutions**:
   - Use a stable, high-speed internet connection
   - Download during off-peak hours
   - Consider using a cloud instance with better bandwidth

### Performance Tips

1. **First Run**: The initial model download takes time (~15GB)
2. **Warm-up**: First inference may be slower as the model loads
3. **Batch Processing**: Process multiple requests together when possible
4. **Caching**: The model is cached locally after first download

## Integration Examples

### Web Application Integration

```javascript
// Frontend JavaScript example
async function askEducationalQuestion(question, level, subject) {
    const response = await fetch('/chat/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            inputs: [
                {
                    type: 'text',
                    content: question
                }
            ],
            educational_level: level,
            subject: subject
        })
    });
    
    return await response.json();
}
```

### Mobile App Integration

```swift
// iOS Swift example
func askEducationalQuestion(question: String, level: String, subject: String) {
    let url = URL(string: "http://your-api-server.com/chat/")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    
    let body = [
        "inputs": [
            [
                "type": "text",
                "content": question
            ]
        ],
        "educational_level": level,
        "subject": subject
    ]
    
    request.httpBody = try? JSONSerialization.data(withJSONObject: body)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        // Handle response
    }.resume()
}
```

## Best Practices

1. **Educational Focus**: Always frame questions in educational context
2. **Age Appropriateness**: Specify the correct educational level
3. **Subject Specificity**: Use appropriate subject categories
4. **Reasoning**: Enable reasoning for complex topics
5. **Multimodal**: Combine text, images, and audio for rich learning experiences

## Support and Resources

- **Model Documentation**: https://huggingface.co/google/gemma-3n-E4B-it
- **API Documentation**: http://localhost:8000/docs
- **Educational Examples**: See the `/examples` directory
- **Community**: Join educational AI discussions on relevant forums

The Gemma 3N E4B model represents the cutting edge of educational AI, providing sophisticated, age-appropriate, and subject-specific assistance for learners at all levels.
