#!/usr/bin/env python3
"""
Test script to verify the installation and basic functionality.

This script tests the basic imports and configuration loading
without requiring the full model to be loaded.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def test_imports():
    """Test that all core modules can be imported."""
    print("Testing imports...")
    
    try:
        from gemma_3n.core.config import get_settings
        print("✓ Core config import successful")
        
        from gemma_3n.core.models import ChatRequest, ChatResponse
        print("✓ Core models import successful")
        
        from gemma_3n.core.exceptions import EducationalAssistantError
        print("✓ Core exceptions import successful")
        
        from gemma_3n.services.content_filter import ContentFilter
        print("✓ Content filter import successful")
        
        from gemma_3n.services.input_processor import InputProcessor
        print("✓ Input processor import successful")
        
        from gemma_3n.services.chat_service import ChatService
        print("✓ Chat service import successful")
        
        from gemma_3n.api import app
        print("✓ FastAPI app import successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from gemma_3n.core.config import get_settings
        
        settings = get_settings()
        print(f"✓ Settings loaded successfully")
        print(f"  - App name: {settings.app_name}")
        print(f"  - Model name: {settings.model_name}")
        print(f"  - Device: {settings.device}")
        print(f"  - Debug mode: {settings.debug}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_content_filter():
    """Test content filtering functionality."""
    print("\nTesting content filter...")
    
    try:
        from gemma_3n.core.config import get_settings
        from gemma_3n.services.content_filter import ContentFilter
        
        settings = get_settings()
        content_filter = ContentFilter(settings)
        
        # Test educational content
        educational_queries = [
            "Can you help me solve this math problem?",
            "Explain photosynthesis in simple terms",
            "What is the difference between mitosis and meiosis?",
            "How do I write a good essay introduction?"
        ]
        
        for query in educational_queries:
            is_educational = content_filter.is_educational_content(query)
            print(f"  {'✓' if is_educational else '✗'} '{query[:50]}...' -> {is_educational}")
        
        # Test non-educational content
        non_educational_queries = [
            "How to hack into a computer system?",
            "What's the weather like today?",
            "Tell me a joke",
            "What's your favorite movie?"
        ]
        
        print("\n  Non-educational content (should be rejected):")
        for query in non_educational_queries:
            is_educational = content_filter.is_educational_content(query)
            print(f"  {'✗' if is_educational else '✓'} '{query[:50]}...' -> {is_educational}")
        
        return True
        
    except Exception as e:
        print(f"✗ Content filter test failed: {e}")
        return False


def test_api_structure():
    """Test FastAPI application structure."""
    print("\nTesting API structure...")
    
    try:
        from gemma_3n.api import app
        
        # Check routes
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health/", "/health/ready", "/chat/", "/sessions/"]
        
        print(f"✓ FastAPI app created successfully")
        print(f"  - Available routes: {len(routes)}")
        
        for expected in expected_routes:
            if any(expected in route for route in routes):
                print(f"  ✓ Route found: {expected}")
            else:
                print(f"  ✗ Route missing: {expected}")
        
        return True
        
    except Exception as e:
        print(f"✗ API structure test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("Gemma 3N Educational Chat API - Installation Test")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_configuration,
        test_content_filter,
        test_api_structure,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Installation appears to be successful.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your settings")
        print("2. Run 'python main.py' to start the API server")
        print("3. Visit http://localhost:8000/docs for API documentation")
        return True
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed: uv sync")
        print("2. Check that Python 3.12+ is being used")
        print("3. Verify the project structure is correct")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
